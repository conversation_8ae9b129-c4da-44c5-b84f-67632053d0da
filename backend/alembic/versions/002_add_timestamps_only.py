"""Add timestamp columns to users table

Revision ID: 002_add_timestamps_only
Revises:
Create Date: 2024-08-09 10:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.sql import func
from sqlalchemy import inspect


# revision identifiers, used by Alembic.
revision = '002_add_timestamps_only'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('users')]

    # Add timestamp columns if they don't exist
    if 'created_at' not in columns:
        op.add_column('users', sa.Column('created_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False))
        print("✅ Added created_at column")
    else:
        print("ℹ️  created_at column already exists")

    if 'updated_at' not in columns:
        op.add_column('users', sa.Column('updated_at', sa.DateTime(timezone=True), server_default=func.now(), nullable=False))
        print("✅ Added updated_at column")
    else:
        print("ℹ️  updated_at column already exists")

    if 'deleted_at' not in columns:
        op.add_column('users', sa.Column('deleted_at', sa.DateTime(timezone=True), nullable=True))
        print("✅ Added deleted_at column")
    else:
        print("ℹ️  deleted_at column already exists")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    conn = op.get_bind()
    inspector = inspect(conn)
    columns = [col['name'] for col in inspector.get_columns('users')]

    # Remove timestamp columns if they exist
    if 'deleted_at' in columns:
        op.drop_column('users', 'deleted_at')
        print("✅ Removed deleted_at column")

    if 'updated_at' in columns:
        op.drop_column('users', 'updated_at')
        print("✅ Removed updated_at column")

    if 'created_at' in columns:
        op.drop_column('users', 'created_at')
        print("✅ Removed created_at column")
    # ### end Alembic commands ###
