#!/usr/bin/env python3
"""
Test script để kiểm tra login API
"""
import requests
import json

def test_login():
    """Test login endpoint"""
    url = "http://localhost:8000/api/v1/auth/login"
    
    # Test data
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        print("🔵 Testing login endpoint...")
        print(f"URL: {url}")
        print(f"Data: {login_data}")
        
        response = requests.post(url, json=login_data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"Response: {json.dumps(data, indent=2)}")
            
            # Test token
            if 'access_token' in data:
                print("✅ Access token received")
            if 'user' in data:
                print("✅ User data received")
                print(f"User: {data['user']}")
        else:
            print("❌ Login failed!")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_register():
    """Test register endpoint"""
    url = "http://localhost:8000/api/v1/auth/register"
    
    # Test data
    register_data = {
        "username": "testuser",
        "password": "testpass123"
    }
    
    try:
        print("\n🔵 Testing register endpoint...")
        print(f"URL: {url}")
        print(f"Data: {register_data}")
        
        response = requests.post(url, json=register_data)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 201:
            data = response.json()
            print("✅ Register successful!")
            print(f"Response: {json.dumps(data, indent=2)}")
        else:
            print("❌ Register failed!")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print("🚀 Starting API tests...")
    
    # Test health endpoint first
    try:
        health_response = requests.get("http://localhost:8000/health")
        if health_response.status_code == 200:
            print("✅ Backend is running")
        else:
            print("❌ Backend health check failed")
            exit(1)
    except:
        print("❌ Cannot connect to backend")
        exit(1)
    
    # Run tests
    test_register()
    test_login()
